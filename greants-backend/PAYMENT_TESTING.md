# Payment Integration Testing Guide

This guide explains how to test the payment integration in the signup process.

## Prerequisites

1. **Environment Setup**: Ensure your `.env` file has the Lahza configuration:
   ```env
   LAHZA_PUBLIC_KEY=pk_test_your_test_key
   LAHZA_SECRET_KEY=sk_test_your_secret_key
   LAHZA_API_URL=https://api.lahza.io
   LAHZA_WEBHOOK_SECRET=your_webhook_secret
   LAHZA_CURRENCY=ILS
   LAHZA_MODE=test
   ```

2. **Database Setup**: Run migrations to ensure settings table exists:
   ```bash
   php artisan migrate
   ```

3. **Settings Configuration**: Verify subscription settings exist in the database:
   - `subscription_amount`: The subscription price
   - `subscription_currency`: The currency (ILS, USD, etc.)
   - `subscription_description`: Description of the subscription

## Testing Steps

### 1. Test Configuration
Visit: `http://your-domain.com/api/test/payment-config`

This should return:
```json
{
  "lahza_configured": true,
  "public_key": "pk_***1234",
  "currency": "ILS",
  "mode": "test",
  "subscription_amount": "23",
  "subscription_currency": "ILS",
  "subscription_description": "TEST"
}
```

### 2. Test Signup Flow

1. **Navigate to Signup**: Go to `http://your-domain.com/sing-up`

2. **Fill Registration Form**:
   - First Name: Test
   - Last Name: User
   - Email: <EMAIL>
   - Organization: Test Org
   - Mobile: +**********
   - Type: Select "Subscriptions"
   - Password: password123

3. **Submit Form**: Click "Register"

4. **Payment Modal**: Should appear with:
   - Amount: 23 ILS
   - Email: <EMAIL>
   - "Proceed to Payment" button

5. **Payment Process**: Click "Proceed to Payment"
   - Lahza payment window should open
   - Use test card details provided by Lahza
   - Complete the payment

6. **Success**: After successful payment:
   - User account should be created
   - User type should be set to "subscribe"
   - Payment record should be created
   - Success modal should appear

### 3. Test Payment Webhook

The webhook endpoint `/api/webhooks/lahza` handles payment notifications:

- **Success Event**: Updates payment status to successful
- **Failure Event**: Updates payment status to failed

### 4. Verify Database Records

After successful payment, check:

1. **Users Table**: New user with `type = 'subscribe'`
2. **Payments Table**: Payment record with status 'success'

## Test Card Details

Use Lahza's test card details for testing:
- Card Number: ****************
- Expiry: Any future date
- CVV: Any 3 digits

## Troubleshooting

### Common Issues

1. **"Payment gateway not initialized"**:
   - Check Lahza public key in `.env`
   - Verify Lahza SDK is loaded
   - Check browser console for errors

2. **"Amount: N/A" in payment modal**:
   - Check subscription settings in database
   - Verify settings are properly formatted
   - Check Livewire component logs

3. **Payment window doesn't open**:
   - Check browser popup blockers
   - Verify Lahza SDK initialization
   - Check JavaScript console for errors

4. **Webhook not working**:
   - Verify webhook URL in Lahza dashboard
   - Check webhook secret configuration
   - Review server logs for errors

### Debug Commands

```bash
# Check settings
php artisan tinker --execute="App\Models\Setting::where('group', 'subscription')->get()->each(function(\$s) { echo \$s->key . ': ' . \$s->value . PHP_EOL; });"

# Check Lahza config
php artisan tinker --execute="echo 'Key: ' . config('services.lahza.key') . PHP_EOL; echo 'Mode: ' . config('services.lahza.mode') . PHP_EOL;"

# Check recent payments
php artisan tinker --execute="App\Models\Payment::latest()->take(5)->get()->each(function(\$p) { echo 'ID: ' . \$p->id . ', Status: ' . \$p->status . ', Amount: ' . \$p->amount . PHP_EOL; });"
```

## Production Deployment

Before going live:

1. **Update Environment**:
   - Change `LAHZA_MODE=live`
   - Use production Lahza keys
   - Update webhook URL in Lahza dashboard

2. **Test Production**:
   - Test with small amounts first
   - Verify webhook handling
   - Monitor payment logs

3. **Security**:
   - Ensure HTTPS is enabled
   - Verify webhook signature validation
   - Monitor for suspicious activity
