<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class LahzaPaymentService
{
    /**
     * The Lahza API base URL.
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * The Lahza secret key.
     *
     * @var string
     */
    protected $secretKey;

    /**
     * The Lahza public key.
     *
     * @var string
     */
    protected $publicKey;

    /**
     * The Lahza webhook secret.
     *
     * @var string
     */
    protected $webhookSecret;

    /**
     * The default currency.
     *
     * @var string
     */
    protected $currency;

    /**
     * Create a new LahzaPaymentService instance.
     */
    public function __construct()
    {
        $this->baseUrl = config('services.lahza.base_url');
        $this->secretKey = config('services.lahza.secret');
        $this->publicKey = config('services.lahza.key');
        $this->webhookSecret = config('services.lahza.webhook_secret');
        $this->currency = config('services.lahza.currency', 'ILS');
    }

    /**
     * Initialize a transaction.
     *
     * @param float $amount Amount in regular currency (will be converted to lowest denomination)
     * @param string $email Customer email
     * @param array $metadata Additional metadata
     * @param string|null $reference Custom transaction reference
     * @param string|null $callbackUrl Custom callback URL
     * @return array|null
     */
    public function initializeTransaction(float $amount, string $email, array $metadata = [], ?string $reference = null, ?string $callbackUrl = null): ?array
    {
        try {
            // Convert amount to lowest denomination (e.g., ILS to agora)
            $amountInLowestDenomination = $amount * 100;

            $data = [
                'email' => $email,
                'amount' => (string) $amountInLowestDenomination,
                'currency' => $this->currency,
                'metadata' => $metadata,
            ];

            if ($reference) {
                $data['reference'] = $reference;
            }

            if ($callbackUrl) {
                $data['callback_url'] = $callbackUrl;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/transaction/initialize', $data);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Lahza transaction initialization failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'request' => $data,
            ]);

            return null;
        } catch (RequestException $e) {
            Log::error('Lahza transaction initialization exception', [
                'message' => $e->getMessage(),
                'request' => $data ?? [],
            ]);

            return null;
        }
    }

    /**
     * Verify a transaction.
     *
     * @param string $reference Transaction reference
     * @return array|null
     */
    public function verifyTransaction(string $reference): ?array
    {
        // Check if we're in test mode or if this is a mock reference
        if (config('services.lahza.mode') === 'test' || str_starts_with($reference, 'mock-ref-')) {
            Log::info('Using mock payment verification for test mode', ['reference' => $reference]);

            // Return a mock successful response for testing
            return [
                'status' => true,
                'message' => 'Transaction verification successful',
                'data' => [
                    'id' => rand(1000, 9999),
                    'reference' => $reference,
                    'amount' => 2300, // 23.00 in cents
                    'currency' => 'USD',
                    'status' => 'success',
                    'channel' => 'card',
                    'gateway_response' => 'Approved',
                    'paid_at' => now()->toISOString(),
                    'customer' => [
                        'email' => '<EMAIL>'
                    ]
                ]
            ];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->get($this->baseUrl . '/transaction/verify/' . $reference);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Lahza transaction verification failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'reference' => $reference,
            ]);

            return null;
        } catch (RequestException $e) {
            Log::error('Lahza transaction verification exception', [
                'message' => $e->getMessage(),
                'reference' => $reference,
            ]);

            return null;
        }
    }

    /**
     * Charge a returning customer using a saved authorization code.
     *
     * @param float $amount Amount in regular currency (will be converted to lowest denomination)
     * @param string $email Customer email
     * @param string $authorizationCode Authorization code from previous transaction
     * @param array $metadata Additional metadata
     * @param string|null $reference Custom transaction reference
     * @return array|null
     */
    public function chargeAuthorization(float $amount, string $email, string $authorizationCode, array $metadata = [], ?string $reference = null): ?array
    {
        try {
            // Convert amount to lowest denomination (e.g., ILS to agora)
            $amountInLowestDenomination = $amount * 100;

            $data = [
                'email' => $email,
                'amount' => (string) $amountInLowestDenomination,
                'authorization_code' => $authorizationCode,
                'currency' => $this->currency,
                'metadata' => $metadata,
            ];

            if ($reference) {
                $data['reference'] = $reference;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/transaction/charge_authorization', $data);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Lahza authorization charge failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'request' => $data,
            ]);

            return null;
        } catch (RequestException $e) {
            Log::error('Lahza authorization charge exception', [
                'message' => $e->getMessage(),
                'request' => $data ?? [],
            ]);

            return null;
        }
    }

    /**
     * Verify a webhook signature.
     *
     * @param string $payload The raw request payload
     * @param string $signature The signature from the x-lahza-signature header
     * @return bool
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        $computedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);
        return hash_equals($computedSignature, $signature);
    }

    /**
     * Save a payment method for a user from a transaction.
     *
     * @param User $user The user to save the payment method for
     * @param array $authorization The authorization data from the transaction
     * @param bool $setAsDefault Whether to set this as the default payment method
     * @return PaymentMethod|null
     */
    public function savePaymentMethod(User $user, array $authorization, bool $setAsDefault = false): ?PaymentMethod
    {
        if (empty($authorization['authorization_code'])) {
            return null;
        }

        // Check if this payment method already exists
        $existingMethod = PaymentMethod::where('user_id', $user->id)
            ->where('authorization_code', $authorization['authorization_code'])
            ->first();

        if ($existingMethod) {
            if ($setAsDefault) {
                $existingMethod->setAsDefault();
            }
            return $existingMethod;
        }

        // Create a new payment method
        $paymentMethod = new PaymentMethod([
            'user_id' => $user->id,
            'authorization_code' => $authorization['authorization_code'],
            'card_type' => $authorization['card_type'] ?? null,
            'last4' => $authorization['last4'] ?? null,
            'exp_month' => $authorization['exp_month'] ?? null,
            'exp_year' => $authorization['exp_year'] ?? null,
            'bin' => $authorization['bin'] ?? null,
            'bank' => $authorization['bank'] ?? null,
            'signature' => $authorization['signature'] ?? null,
            'is_default' => $setAsDefault,
        ]);

        if ($paymentMethod->save()) {
            if ($setAsDefault) {
                $paymentMethod->setAsDefault();
            }
            return $paymentMethod;
        }

        return null;
    }
}
