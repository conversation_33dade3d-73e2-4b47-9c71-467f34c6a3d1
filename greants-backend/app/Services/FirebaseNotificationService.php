<?php

namespace App\Services;

use <PERSON>reait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification as FirebaseNotification;
use Illuminate\Support\Facades\Log;

class FirebaseNotificationService
{
    protected $messaging;

    public function __construct()
    {
        try {
            $factory = new Factory();

            // Check if the service account file exists
            $serviceAccountPath = config('firebase.credentials.file');
            if (file_exists($serviceAccountPath)) {
                $factory = $factory->withServiceAccount($serviceAccountPath);
                $this->messaging = $factory->createMessaging();
            } else {
                Log::warning("Firebase credentials file not found at: {$serviceAccountPath}. Firebase notifications will be disabled.");
                $this->messaging = null;
            }
        } catch (\Exception $e) {
            Log::error("Error initializing Firebase: " . $e->getMessage());
            $this->messaging = null;
        }
    }

    /**
     * Send a simple notification with title and body
     *
     * @param string $token FCM token
     * @param string $title Notification title
     * @param string $body Notification body
     * @return mixed
     */
    public function sendNotification($token, $title, $body)
    {
        if (!$this->messaging) {
            Log::warning('Firebase messaging not initialized. Notification not sent.');
            return false;
        }

        // Create a message
        $message = CloudMessage::fromArray([
            'token' => $token,
            'notification' => [
                'title' => $title,
                'body' => $body
            ]
        ]);

        return $this->messaging->send($message);
    }

    /**
     * Send a notification with data payload for form submissions
     *
     * @param string $token FCM token
     * @param string $title Notification title
     * @param string $body Notification body
     * @param array $data Additional data to include in the notification
     * @return mixed
     */
    public function sendFormNotification($token, $title, $body, array $data = [])
    {
        if (!$this->messaging) {
            Log::warning('Firebase messaging not initialized. Form notification not sent.');
            return false;
        }

        // Create a message with notification and data
        $message = CloudMessage::fromArray([
            'token' => $token,
            'notification' => [
                'title' => $title,
                'body' => $body
            ],
            'data' => $data
        ]);

        return $this->messaging->send($message);
    }

    /**
     * Send notification to multiple tokens
     *
     * @param array $tokens Array of FCM tokens
     * @param string $title Notification title
     * @param string $body Notification body
     * @param array $data Additional data to include in the notification
     * @return array Results of sending to each token
     */
    public function sendMulticastNotification(array $tokens, $title, $body, array $data = [])
    {
        $results = [];

        foreach ($tokens as $token) {
            try {
                $results[$token] = $this->sendFormNotification($token, $title, $body, $data);
            } catch (\Exception $e) {
                Log::error("Failed to send notification to token: {$token}. Error: " . $e->getMessage());
                $results[$token] = false;
            }
        }

        return $results;
    }
}
