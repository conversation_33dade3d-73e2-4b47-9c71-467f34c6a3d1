<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\Payment;
use App\Models\Setting;
use Livewire\Component;
use Livewire\Attributes\On;
use App\Services\NotificationService;
use App\Services\LahzaPaymentService;
use Illuminate\Support\Facades\Log;

class RegisterForm extends Component
{
    public $first_name, $last_name, $email, $organization, $mobile, $type, $password;
    public $subscriptionAmount, $subscriptionCurrency, $subscriptionDescription;
    public $paymentReference, $userId;

    protected $rules = [
        'first_name'    => 'required|string|max:255',
        'last_name'     => 'required|string|max:255',
        'email'         => 'required|email|unique:users,email',
        'organization'  => 'required|string|max:255',
        'mobile'        => 'required|string|max:15',
        'type'          => 'required|in:free,subscribe',
        'password'      => 'required|string|min:8',
    ];

    public function mount()
    {
        // Load subscription settings from the database
        $this->subscriptionAmount = $this->getSettingValue('subscription_amount', 499);
        $this->subscriptionCurrency = $this->getSettingValue('subscription_currency', 'ILS');
        $this->subscriptionDescription = $this->getSettingValue('subscription_description', 'Access to the Database with no expiry date');

        // Ensure currency is a simple string, not JSON
        if (is_string($this->subscriptionCurrency) && str_contains($this->subscriptionCurrency, '{')) {
            $decoded = json_decode($this->subscriptionCurrency, true);
            $this->subscriptionCurrency = $decoded['en'] ?? $decoded[array_key_first($decoded)] ?? 'ILS';
        }

        // Debug log the loaded settings
        Log::info('Subscription settings loaded', [
            'amount' => $this->subscriptionAmount,
            'currency' => $this->subscriptionCurrency,
            'description' => $this->subscriptionDescription
        ]);
    }

    public function register()
    {
        Log::info('Register method called', [
            'first_name' => $this->first_name,
            'email' => $this->email,
            'type' => $this->type
        ]);

        $this->validate();

        Log::info('Validation passed');

        $user = User::create([
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'organization' => $this->organization,
            'mobile' => $this->mobile,
            'type' => $this->type,
            'password' => bcrypt($this->password),
        ]);

        // Store user ID for payment processing
        $this->userId = $user->id;

        // Send notification (temporarily disabled for debugging)
        try {
            $notificationService = app(NotificationService::class);
            $notificationService->sendUserRegistrationNotification([
                'id' => $user->id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'organization' => $user->organization,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send notification: ' . $e->getMessage());
            // Continue with registration even if notification fails
        }

        session()->flash('success', 'Account created successfully!');
        if($this->type == 'subscribe'){
            // Generate a unique reference for payment
            $this->paymentReference = 'GR-REG-' . time() . '-' . $user->id;

            // Log the payment details for debugging
            Log::info('Dispatching payment modal', [
                'amount' => $this->subscriptionAmount,
                'currency' => $this->subscriptionCurrency,
                'email' => $this->email,
                'reference' => $this->paymentReference,
                'userId' => $user->id
            ]);

            // Show payment modal
            $this->dispatch('show-payment-modal', [
                'amount' => $this->subscriptionAmount,
                'currency' => $this->subscriptionCurrency,
                'email' => $this->email,
                'reference' => $this->paymentReference,
                'userId' => $user->id
            ]);
        } else {
            $this->dispatch('show-success-modal');
            $this->reset();
        }
    }

    #[On('completePayment')]
    public function completePayment($transactionReference = null)
    {
        // Handle both array and direct parameter formats
        if (is_array($transactionReference) && isset($transactionReference['transactionReference'])) {
            $reference = $transactionReference['transactionReference'];
        } else {
            $reference = $transactionReference;
        }

        if (!$reference) {
            Log::error('No transaction reference provided for payment completion');
            $this->dispatch('payment-failed');
            return false;
        }

        // Verify the payment with Lahza
        Log::info('Starting payment verification', ['reference' => $reference]);

        $paymentService = app(LahzaPaymentService::class);
        $verificationResult = $paymentService->verifyTransaction($reference);

        Log::info('Payment verification result', [
            'reference' => $reference,
            'result' => $verificationResult,
            'has_data' => isset($verificationResult['data']),
            'status' => $verificationResult['data']['status'] ?? 'no status'
        ]);

        if ($verificationResult && isset($verificationResult['data']['status']) && strtolower($verificationResult['data']['status']) === 'success') {
            // Create a payment record
            Payment::create([
                'reference' => $this->paymentReference,
                'payable_id' => $this->userId,
                'payable_type' => User::class,
                'user_id' => $this->userId,
                'amount' => $this->subscriptionAmount,
                'currency' => $this->subscriptionCurrency,
                'status' => Payment::STATUS_SUCCESS,
                'payment_method' => $verificationResult['data']['channel'] ?? 'lahza',
                'payment_channel' => $verificationResult['data']['channel'] ?? 'lahza',
                'gateway_response' => $verificationResult['data']['gateway_response'] ?? 'Payment successful',
                'metadata' => [
                    'type' => 'subscription',
                    'email' => $this->email,
                ],
                'paid_at' => now(),
            ]);

            // Update user subscription status if needed
            $user = User::find($this->userId);
            if ($user) {
                $user->type = User::SUBSCRIBE;
                $user->save();
            }

            // Show success modal
            $this->dispatch('show-success-modal');
            $this->reset();

            return true;
        }

        // Payment failed
        $this->dispatch('payment-failed');
        return false;
    }

    /**
     * Get setting value with proper handling of translatable fields
     */
    private function getSettingValue($key, $default = null)
    {
        try {
            // Get setting directly from database
            $setting = Setting::where('key', $key)->first();

            if (!$setting) {
                return $default;
            }

            $value = $setting->value;

            // If the setting is translatable, extract the English value
            if ($setting->is_translatable && is_string($value)) {
                // Try to decode as JSON
                $decoded = json_decode($value, true);
                if (is_array($decoded)) {
                    // Return English value if available, otherwise first available value
                    return $decoded['en'] ?? reset($decoded) ?? $default;
                }
            }

            // Special handling for currency - ensure it's always a simple string
            if ($key === 'subscription_currency' && is_string($value) && str_contains($value, '{')) {
                $decoded = json_decode($value, true);
                if (is_array($decoded)) {
                    return $decoded['en'] ?? reset($decoded) ?? $default;
                }
            }

            // For non-translatable settings or if JSON decode fails, return the raw value
            return $value ?: $default;
        } catch (\Exception $e) {
            Log::error('Error getting setting value', ['key' => $key, 'error' => $e->getMessage()]);
            return $default;
        }
    }

    public function render()
    {
        return view('livewire.register-form');
    }
}
