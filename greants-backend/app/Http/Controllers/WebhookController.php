<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\User;
use App\Services\LahzaPaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    protected $lahzaService;

    public function __construct(LahzaPaymentService $lahzaService)
    {
        $this->lahzaService = $lahzaService;
    }

    /**
     * Handle Lahza payment webhook
     */
    public function lahzaWebhook(Request $request)
    {
        try {
            // Get the raw payload and signature
            $payload = $request->getContent();
            $signature = $request->header('x-lahza-signature');

            // Verify the webhook signature
            if (!$this->lahzaService->verifyWebhookSignature($payload, $signature)) {
                Log::warning('Invalid Lahza webhook signature', [
                    'signature' => $signature,
                    'payload' => $payload
                ]);
                return response()->json(['error' => 'Invalid signature'], 401);
            }

            // Parse the webhook data
            $data = json_decode($payload, true);

            if (!$data || !isset($data['event'])) {
                Log::warning('Invalid Lahza webhook payload', ['payload' => $payload]);
                return response()->json(['error' => 'Invalid payload'], 400);
            }

            // Handle different webhook events
            switch ($data['event']) {
                case 'charge.success':
                    $this->handleSuccessfulPayment($data['data']);
                    break;
                
                case 'charge.failed':
                    $this->handleFailedPayment($data['data']);
                    break;
                
                default:
                    Log::info('Unhandled Lahza webhook event', ['event' => $data['event']]);
                    break;
            }

            return response()->json(['status' => 'success'], 200);

        } catch (\Exception $e) {
            Log::error('Lahza webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $request->getContent()
            ]);
            
            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Handle successful payment
     */
    private function handleSuccessfulPayment($data)
    {
        $reference = $data['reference'] ?? null;
        
        if (!$reference) {
            Log::warning('No reference in successful payment webhook', ['data' => $data]);
            return;
        }

        // Find the payment record
        $payment = Payment::where('reference', $reference)->first();
        
        if (!$payment) {
            Log::warning('Payment not found for reference', ['reference' => $reference]);
            return;
        }

        // Update payment status if not already successful
        if ($payment->status !== Payment::STATUS_SUCCESS) {
            $payment->update([
                'status' => Payment::STATUS_SUCCESS,
                'payment_method' => $data['channel'] ?? 'lahza',
                'payment_channel' => $data['channel'] ?? 'lahza',
                'gateway_response' => $data['gateway_response'] ?? 'Payment successful',
                'paid_at' => now(),
            ]);

            // Update user subscription status if this is a subscription payment
            if ($payment->user_id && isset($payment->metadata['type']) && $payment->metadata['type'] === 'subscription') {
                $user = User::find($payment->user_id);
                if ($user && $user->type !== User::SUBSCRIBE) {
                    $user->update(['type' => User::SUBSCRIBE]);
                }
            }

            Log::info('Payment marked as successful via webhook', [
                'reference' => $reference,
                'payment_id' => $payment->id
            ]);
        }
    }

    /**
     * Handle failed payment
     */
    private function handleFailedPayment($data)
    {
        $reference = $data['reference'] ?? null;
        
        if (!$reference) {
            Log::warning('No reference in failed payment webhook', ['data' => $data]);
            return;
        }

        // Find the payment record
        $payment = Payment::where('reference', $reference)->first();
        
        if (!$payment) {
            Log::warning('Payment not found for reference', ['reference' => $reference]);
            return;
        }

        // Update payment status if not already failed
        if ($payment->status !== Payment::STATUS_FAILED) {
            $payment->update([
                'status' => Payment::STATUS_FAILED,
                'gateway_response' => $data['gateway_response'] ?? 'Payment failed',
            ]);

            Log::info('Payment marked as failed via webhook', [
                'reference' => $reference,
                'payment_id' => $payment->id
            ]);
        }
    }
}
