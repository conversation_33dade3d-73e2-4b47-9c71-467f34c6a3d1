<?php

use App\Http\Controllers\Admin\Auth\AuthController;
use App\Http\Controllers\Admin\Client\ClientController;
use App\Http\Controllers\Admin\Country\CountryController;
use App\Http\Controllers\Admin\FeesCoverage\FeesCoverageController;
use App\Http\Controllers\Admin\FieldDatabase\FieldDatabaseController;
use App\Http\Controllers\Admin\FieldOpportunity\FieldOpportunityController;
use App\Http\Controllers\Admin\Funder\FunderController;
use App\Http\Controllers\Admin\HowKnow\HowKnowController;
use App\Http\Controllers\Admin\Language\LanguageController;
use App\Http\Controllers\Admin\Methodology\MethodologyController;
use App\Http\Controllers\Admin\Opportunity\OpportunityController;
use App\Http\Controllers\Admin\Page\PageController;
use App\Http\Controllers\Admin\Partnership\PartnershipController;
use App\Http\Controllers\Admin\Section\SectionController;
use App\Http\Controllers\Admin\Subject\SubjectController;
use App\Http\Controllers\Admin\SupportRequest\SupportRequestController;
use App\Http\Controllers\Admin\Tag\TagController;
use App\Http\Controllers\Admin\Training\TrainingController;
use App\Http\Controllers\Admin\User\UserController;
use App\Http\Controllers\Admin\Payment\PaymentController;
use App\Http\Controllers\WebhookController;
use Illuminate\Support\Facades\Route;


Route::prefix('admin')->group(function () {
    Route::post('auth/login', [AuthController::class, 'login'])->middleware('api');

    Route::middleware(['api','auth:admin-api'])->group(function () {
        Route::post('auth/logout', [AuthController::class, 'logout']);
        Route::post('auth/set-fcm-token', [AuthController::class, 'setFcmToken']);

        // Notification routes
        Route::post('notifications/send', [\App\Http\Controllers\Admin\NotificationController::class, 'sendFormNotification']);
        Route::post('notifications/mark-as-read', [\App\Http\Controllers\Admin\NotificationController::class, 'markAsRead']);
        Route::get('notifications', [\App\Http\Controllers\Admin\NotificationController::class, 'getNotifications']);

        // Dashboard routes
        Route::get('dashboard/stats', [\App\Http\Controllers\Admin\DashboardController::class, 'getStats']);
        Route::get('dashboard/user-chart', [\App\Http\Controllers\Admin\DashboardController::class, 'getUserChartData']);
        Route::get('dashboard/opportunity-chart', [\App\Http\Controllers\Admin\DashboardController::class, 'getOpportunityChartData']);
        Route::get('dashboard/export/{format?}', [\App\Http\Controllers\Admin\DashboardController::class, 'exportDashboardReport']);
    });

    Route::middleware(['api','auth:admin-api'])->group(function () {
        Route::get('countries/filter-options', [CountryController::class, 'getFilterOptions']);
      Route::post('countries/actions',[CountryController::class, 'applyAction']);
        Route::resource('countries', CountryController::class);

//      <-------------------- FieldOpportunity -------------------->
        // Field Opportunities routes with permissions
        Route::get('field-opportunities/filter-options', [FieldOpportunityController::class, 'getFilterOptions']);
        Route::post('field-opportunities/actions',[FieldOpportunityController::class, 'applyAction'])->middleware('permission:edit_field_opportunities');
        Route::get('field-opportunities', [FieldOpportunityController::class, 'index'])->middleware('permission:view_field_opportunities');
        Route::get('field-opportunities/{id}', [FieldOpportunityController::class, 'show'])->middleware('permission:view_field_opportunities');
        Route::post('field-opportunities', [FieldOpportunityController::class, 'store'])->middleware('permission:create_field_opportunities');
        Route::put('field-opportunities/{id}', [FieldOpportunityController::class, 'update'])->middleware('permission:edit_field_opportunities');
        Route::delete('field-opportunities/{id}', [FieldOpportunityController::class, 'destroy'])->middleware('permission:delete_field_opportunities');


//        <-------------------- FieldOpportunity -------------------->


//        <-------------------- FieldDatabase -------------------->
        // Field Databases routes with permissions
        Route::get('field-databases/filter-options', [FieldDatabaseController::class, 'getFilterOptions']);
        Route::post('field-databases/actions',[FieldDatabaseController::class, 'applyAction'])->middleware('permission:edit_field_databases');
        Route::get('field-databases', [FieldDatabaseController::class, 'index'])->middleware('permission:view_field_databases');
        Route::get('field-databases/{id}', [FieldDatabaseController::class, 'show'])->middleware('permission:view_field_databases');
        Route::post('field-databases', [FieldDatabaseController::class, 'store'])->middleware('permission:create_field_databases');
        Route::put('field-databases/{id}', [FieldDatabaseController::class, 'update'])->middleware('permission:edit_field_databases');
        Route::delete('field-databases/{id}', [FieldDatabaseController::class, 'destroy'])->middleware('permission:delete_field_databases');

//        <------------------ FieldDatabase -------------------->



//        <-------------------- Fees Coverage -------------------->

        Route::get('fees-coverages/filter-options', [FeesCoverageController::class, 'getFilterOptions']);
        Route::post('fees-coverages/actions',[FeesCoverageController::class, 'applyAction']);
        Route::resource('fees-coverages', FeesCoverageController::class);

//        <------------------ Fees Coverage -------------------->


//        <-------------------- HowKnow -------------------->
        Route::get('how-knows/filter-options', [HowKnowController::class, 'getFilterOptions']);
        Route::post('how-knows/actions',[HowKnowController::class, 'applyAction']);
        Route::resource('how-knows', HowKnowController::class);

//        <------------------ HowKnow -------------------->

//        <-------------------- Subject -------------------->
        // Subjects routes with permissions
        Route::get('subjects/filter-options', [SubjectController::class, 'getFilterOptions']);
        Route::post('subjects/actions',[SubjectController::class, 'applyAction'])->middleware('permission:edit_subjects');
        Route::get('subjects', [SubjectController::class, 'index'])->middleware('permission:view_subjects');
        Route::get('subjects/{id}', [SubjectController::class, 'show'])->middleware('permission:view_subjects');
        Route::post('subjects', [SubjectController::class, 'store'])->middleware('permission:create_subjects');
        Route::put('subjects/{id}', [SubjectController::class, 'update'])->middleware('permission:edit_subjects');
        Route::delete('subjects/{id}', [SubjectController::class, 'destroy'])->middleware('permission:delete_subjects');

//        <------------------ Subject -------------------->


//        <-------------------- Funder -------------------->
        // Funder import routes - these need to be before the {id} routes to avoid conflicts
        Route::get('funders/import/template', [\App\Http\Controllers\Admin\Funder\FunderImportController::class, 'downloadTemplate'])->middleware('permission:create_funders');
        Route::post('funders/import', [\App\Http\Controllers\Admin\Funder\FunderImportController::class, 'import'])->middleware('permission:create_funders');

        // Funders routes with permissions
        Route::get('funders/filter-options', [FunderController::class, 'getFilterOptions']);
        Route::get('funders/export/{format?}', [FunderController::class, 'exportFunders'])->withoutMiddleware('auth:admin-api');
        Route::post('funders/actions',[FunderController::class, 'applyAction'])->middleware('permission:edit_funders');
        Route::get('funders', [FunderController::class, 'index'])->middleware('permission:view_funders');
        Route::get('funders/{id}', [FunderController::class, 'show'])->middleware('permission:view_funders');
        Route::post('funders', [FunderController::class, 'store'])->middleware('permission:create_funders');
        Route::put('funders/{id}', [FunderController::class, 'update'])->middleware('permission:edit_funders');
        Route::delete('funders/{id}', [FunderController::class, 'destroy'])->middleware('permission:delete_funders');

//        <------------------ Funder -------------------->

//        <!-- <-------------------- Opportunity --------------------> -->
        Route::get('opportunities/filter-options', [OpportunityController::class, 'getFilterOptions']);
        Route::post('opportunities/actions',[OpportunityController::class, 'applyAction']);
        Route::get('opportunities/export/{format?}', [OpportunityController::class, 'export'])->withoutMiddleware('auth:admin-api');
        Route::resource('opportunities', OpportunityController::class);
//     <!-- <------------------ Languages --------------------> -->
Route::get('languages/active', [\App\Http\Controllers\Admin\LanguageController::class, 'getActiveLanguages']);

        Route::get('languages/filter-options', [LanguageController::class, 'getFilterOptions']);
        Route::post('languages/actions',[LanguageController::class, 'applyAction']);
        Route::resource('languages', LanguageController::class);
//     <!-- <------------------ Languages --------------------> -->


//        <!-- <-------------------- Methodology --------------------> -->
        // Methodologies routes with permissions
        Route::get('methodologies/filter-options', [MethodologyController::class, 'getFilterOptions']);
        Route::post('methodologies/actions',[MethodologyController::class, 'applyAction'])->middleware('permission:edit_methodologies');
        Route::get('methodologies', [MethodologyController::class, 'index'])->middleware('permission:view_methodologies');
        Route::get('methodologies/{id}', [MethodologyController::class, 'show'])->middleware('permission:view_methodologies');
        Route::post('methodologies', [MethodologyController::class, 'store'])->middleware('permission:create_methodologies');
        Route::put('methodologies/{id}', [MethodologyController::class, 'update'])->middleware('permission:edit_methodologies');
        Route::delete('methodologies/{id}', [MethodologyController::class, 'destroy'])->middleware('permission:delete_methodologies');
//     <!-- <------------------ Methodology --------------------> -->


//        <!-- <-------------------- Training --------------------> -->
        // Trainings routes with permissions
        Route::get('trainings/filter-options', [TrainingController::class, 'getFilterOptions']);
        Route::post('trainings/actions',[TrainingController::class, 'applyAction'])->middleware('permission:edit_trainings');
        Route::get('trainings', [TrainingController::class, 'index'])->middleware('permission:view_trainings');
        Route::get('trainings/{id}', [TrainingController::class, 'show'])->middleware('permission:view_trainings');
        Route::post('trainings', [TrainingController::class, 'store'])->middleware('permission:create_trainings');
        Route::put('trainings/{id}', [TrainingController::class, 'update'])->middleware('permission:edit_trainings');
        Route::delete('trainings/{id}', [TrainingController::class, 'destroy'])->middleware('permission:delete_trainings');
//     <!-- <------------------ Training --------------------> -->

//        <!-- <-------------------- Course Registration --------------------> -->
        // Course Registration routes
        Route::get('course-registrations/filter-options', [\App\Http\Controllers\Admin\CourseRegistration\CourseRegistrationController::class, 'getFilterOptions']);
        Route::get('course-registrations/stats', [\App\Http\Controllers\Admin\CourseRegistration\CourseRegistrationController::class, 'getStats']);
        Route::post('course-registrations/actions', [\App\Http\Controllers\Admin\CourseRegistration\CourseRegistrationController::class, 'applyAction'])->middleware('permission:edit_trainings');
        Route::get('course-registrations', [\App\Http\Controllers\Admin\CourseRegistration\CourseRegistrationController::class, 'index'])->middleware('permission:view_trainings');
        Route::get('course-registrations/{id}', [\App\Http\Controllers\Admin\CourseRegistration\CourseRegistrationController::class, 'show'])->middleware('permission:view_trainings');
        Route::put('course-registrations/{id}/status', [\App\Http\Controllers\Admin\CourseRegistration\CourseRegistrationController::class, 'updateStatus'])->middleware('permission:edit_trainings');
//     <!-- <------------------ Course Registration --------------------> -->


//        <!-- <-------------------- Section --------------------> -->
        Route::get('sections/filter-options', [SectionController::class, 'getFilterOptions']);
        Route::post('sections/actions',[SectionController::class, 'applyAction']);
        Route::resource('sections', SectionController::class);
        //     <!-- <------------------ Section --------------------> -->

//        -------------------------- Page --------------------------
        Route::get('pages/filter-options', [PageController::class, 'getFilterOptions']);
        Route::post('pages/actions',[PageController::class, 'applyAction']);
        Route::get('pages/{id}/sections', [PageController::class, 'getSections']);
        Route::get('pages/{id}/sections/create', [PageController::class, 'createSection']);
        Route::post('pages/{id}/section', [PageController::class, 'saveSection']);
        Route::put('pages/section/{section_id}', [PageController::class, 'updateSection']);
        Route::get('pages/section/{section_id}', [PageController::class, 'getSection']);
        Route::delete('pages/section/{section_id}', [PageController::class, 'deleteSection']);
        Route::resource('pages', PageController::class);

//        -------------------------- Page --------------------------


//        -------------------------- Client --------------------------
        // Clients routes with permissions
        Route::get('clients/filter-options', [ClientController::class, 'getFilterOptions']);
        Route::post('clients/actions',[ClientController::class, 'applyAction'])->middleware('permission:edit_clients');
        Route::get('clients', [ClientController::class, 'index'])->middleware('permission:view_clients');
        Route::get('clients/{id}', [ClientController::class, 'show'])->middleware('permission:view_clients');
        Route::post('clients', [ClientController::class, 'store'])->middleware('permission:create_clients');
        Route::put('clients/{id}', [ClientController::class, 'update'])->middleware('permission:edit_clients');
        Route::delete('clients/{id}', [ClientController::class, 'destroy'])->middleware('permission:delete_clients');

//        -------------------------- Client --------------------------

        //   ------------------------- User -------------------------
        Route::get('users/filter-options', [UserController::class, 'getFilterOptions']);
        Route::post('users/actions',[UserController::class, 'applyAction']);
        Route::resource('users', UserController::class);
        //   ------------------------- User -------------------------

        //   ------------------------- Roles & Permissions -------------------------
        Route::get('roles/filter-options', [App\Http\Controllers\Admin\Role\RoleController::class, 'filterOptions']);

        // Define only the routes that have corresponding methods in the controller
        Route::get('roles', [App\Http\Controllers\Admin\Role\RoleController::class, 'index'])->name('roles.index');
        Route::post('roles', [App\Http\Controllers\Admin\Role\RoleController::class, 'store'])->name('roles.store');
        Route::get('roles/{id}', [App\Http\Controllers\Admin\Role\RoleController::class, 'show'])->name('roles.show');
        Route::put('roles/{id}', [App\Http\Controllers\Admin\Role\RoleController::class, 'update'])->name('roles.update');
        Route::delete('roles/{id}', [App\Http\Controllers\Admin\Role\RoleController::class, 'destroy'])->name('roles.destroy');

        Route::get('permissions/filter-options', [App\Http\Controllers\Admin\Permission\PermissionController::class, 'filterOptions']);

        // Define only the routes that have corresponding methods in the controller
        Route::get('permissions', [App\Http\Controllers\Admin\Permission\PermissionController::class, 'index'])->name('permissions.index');
        Route::post('permissions', [App\Http\Controllers\Admin\Permission\PermissionController::class, 'store'])->name('permissions.store');
        Route::get('permissions/{permission}', [App\Http\Controllers\Admin\Permission\PermissionController::class, 'show'])->name('permissions.show');
        Route::put('permissions/{permission}', [App\Http\Controllers\Admin\Permission\PermissionController::class, 'update'])->name('permissions.update');
        Route::delete('permissions/{permission}', [App\Http\Controllers\Admin\Permission\PermissionController::class, 'destroy'])->name('permissions.destroy');
        //   ------------------------- Roles & Permissions -------------------------

        //   ------------------------- Support Request -------------------------
        Route::get('support-requests', [SupportRequestController::class, 'index']);
        Route::get('support-requests/{id}', [SupportRequestController::class, 'show']);
        Route::put('support-requests/{id}', [SupportRequestController::class, 'update']);
        Route::delete('support-requests/{id}', [SupportRequestController::class, 'destroy']);

        //  ------------------------- Partnership -------------------------
        // Partnerships routes with permissions
        Route::get('partnerships/filter-options', [PartnershipController::class, 'getFilterOptions']);
        Route::post('partnerships/actions',[PartnershipController::class, 'applyAction'])->middleware('permission:edit_partnerships');
        Route::get('partnerships', [PartnershipController::class, 'index'])->middleware('permission:view_partnerships');
        Route::get('partnerships/{id}', [PartnershipController::class, 'show'])->middleware('permission:view_partnerships');
        Route::post('partnerships', [PartnershipController::class, 'store'])->middleware('permission:create_partnerships');
        Route::put('partnerships/{id}', [PartnershipController::class, 'update'])->middleware('permission:edit_partnerships');
        Route::delete('partnerships/{id}', [PartnershipController::class, 'destroy'])->middleware('permission:delete_partnerships');
        //  ------------------------- Partnership -------------------------


        // ------------------------- Tag -------------------------
        Route::get('tags/filter-options', [TagController::class, 'getFilterOptions']);
        Route::post('tags/actions',[TagController::class, 'applyAction']);
        Route::resource('tags', TagController::class);

        // ------------------------- Tag -------------------------

        // ------------------------- News -------------------------
        Route::get('news/filter-options', [\App\Http\Controllers\Admin\News\NewsController::class, 'getFilterOptions']);
        Route::post('news/actions',[\App\Http\Controllers\Admin\News\NewsController::class, 'applyAction']);
        Route::resource('news', \App\Http\Controllers\Admin\News\NewsController::class);


        // ------------------------- News -------------------------


        // ------------------------- Team -------------------------
        Route::get('teams/filter-options', [\App\Http\Controllers\Admin\Team\TeamController::class, 'getFilterOptions']);
        Route::post('teams/actions',[\App\Http\Controllers\Admin\Team\TeamController::class, 'applyAction']);
        Route::resource('teams', \App\Http\Controllers\Admin\Team\TeamController::class);
        // ------------------------- Team -------------------------

        // ------------------------- Proposal Booking -------------------------
        Route::get('proposal-bookings/filter-options', [\App\Http\Controllers\Admin\ProposalBooking\ProposalBookingController::class, 'getFilterOptions']);
        Route::post('proposal-bookings/actions',[\App\Http\Controllers\Admin\ProposalBooking\ProposalBookingController::class, 'applayAction']);
        Route::resource('proposal-bookings', \App\Http\Controllers\Admin\ProposalBooking\ProposalBookingController::class);

        // ------------------------- Payments -------------------------
        Route::get('payments', [PaymentController::class, 'index']);
        Route::get('payments/{id}', [PaymentController::class, 'show']);
        Route::post('payments/initialize', [PaymentController::class, 'initialize']);
        Route::post('payments/verify', [PaymentController::class, 'verify']);
        Route::post('payments/charge-authorization', [PaymentController::class, 'chargeAuthorization']);
        Route::get('payment-methods', [PaymentController::class, 'getPaymentMethods']);
        Route::post('payment-methods/set-default', [PaymentController::class, 'setDefaultPaymentMethod']);
        Route::delete('payment-methods/{id}', [PaymentController::class, 'deletePaymentMethod']);

        // ------------------------- Settings -------------------------
        Route::get('settings', [\App\Http\Controllers\Admin\Settings\SettingsController::class, 'index']);
        Route::get('settings/{id}', [\App\Http\Controllers\Admin\Settings\SettingsController::class, 'show']);
        Route::put('settings/{id}', [\App\Http\Controllers\Admin\Settings\SettingsController::class, 'update']);
        Route::post('settings/update-multiple', [\App\Http\Controllers\Admin\Settings\SettingsController::class, 'updateMultiple']);
        Route::get('settings/group/{group}', [\App\Http\Controllers\Admin\Settings\SettingsController::class, 'getByGroup']);

        // ------------------------- Multilingual Settings -------------------------
        Route::get('multilingual-settings', [\App\Http\Controllers\Admin\Settings\MultilingualSettingsController::class, 'index']);
        Route::get('multilingual-settings/{id}', [\App\Http\Controllers\Admin\Settings\MultilingualSettingsController::class, 'show']);
        Route::put('multilingual-settings/{id}', [\App\Http\Controllers\Admin\Settings\MultilingualSettingsController::class, 'update']);
        Route::post('multilingual-settings/update-multiple', [\App\Http\Controllers\Admin\Settings\MultilingualSettingsController::class, 'updateMultiple']);
        Route::get('multilingual-settings/group/{group}', [\App\Http\Controllers\Admin\Settings\MultilingualSettingsController::class, 'getByGroup']);

        // ------------------------- Languages -------------------------
        // Route::get('languages', [\App\Http\Controllers\Admin\LanguageController::class, 'index']);
        Route::post('languages', [\App\Http\Controllers\Admin\LanguageController::class, 'store']);
        Route::get('languages/{id}', [\App\Http\Controllers\Admin\LanguageController::class, 'show']);
        Route::put('languages/{id}', [\App\Http\Controllers\Admin\LanguageController::class, 'update']);
        Route::delete('languages/{id}', [\App\Http\Controllers\Admin\LanguageController::class, 'destroy']);
        Route::put('languages/{id}/toggle-active', [\App\Http\Controllers\Admin\LanguageController::class, 'toggleActive']);

        // ------------------------- Subscription Settings -------------------------
        Route::get('subscription-settings', [\App\Http\Controllers\Admin\Settings\SubscriptionSettingsController::class, 'index']);
        Route::put('subscription-settings', [\App\Http\Controllers\Admin\Settings\SubscriptionSettingsController::class, 'update']);
        Route::get('subscription-price', [\App\Http\Controllers\Admin\Settings\SubscriptionSettingsController::class, 'getSubscriptionPrice']);
    });


});

// Lahza Webhook route (outside authentication)
Route::post('webhooks/lahza', [WebhookController::class, 'lahzaWebhook']);

// Test route for payment configuration
Route::get('test/payment-config', function() {
    $registerForm = new \App\Livewire\RegisterForm();
    $registerForm->mount();

    return response()->json([
        'lahza_configured' => !empty(config('services.lahza.key')),
        'public_key' => config('services.lahza.key') ? 'pk_***' . substr(config('services.lahza.key'), -4) : null,
        'currency' => config('services.lahza.currency'),
        'mode' => config('services.lahza.mode'),
        'subscription_amount' => $registerForm->subscriptionAmount,
        'subscription_currency' => $registerForm->subscriptionCurrency,
        'subscription_description' => $registerForm->subscriptionDescription,
    ]);
});

// Test route for payment verification
Route::get('test/payment-verify/{reference}', function($reference) {
    $paymentService = app(\App\Services\LahzaPaymentService::class);
    $result = $paymentService->verifyTransaction($reference);

    return response()->json([
        'reference' => $reference,
        'verification_result' => $result,
        'is_successful' => $result && isset($result['data']['status']) && strtolower($result['data']['status']) === 'success'
    ]);
});
