<!-- ======= Proposal Writing Guide ======= -->
<section class="writing-guide">
    <div class="container mb-4">
        <div class="row">
            <div class="col-md-4 d-flex align-items-stretch ">
                <div class="w-100">

                    <!-- Testimonial Item -->
                    @foreach($section->content['reviews'] as $review)
                    <div class="mb-2 proposal-Testimonial p-3 ">
                        <p class="mb-0 mb-4 text-primary">
                            {{ getDataByLang($review, 'review') }}
                        </p>
                        <div class="d-flex justify-content-between align-items-end">

                            <div class="d-flex align-items-center">
                                <img src="{{asset($review['image'][0])}}" alt="Nadine Mohammed" class="rounded-circle me-2"
                                     width="45" height="45" />
                                <div>
                                    <h6 class="mb-0">
                                        {{ getDataByLang($review, 'name') }}
                                    </h6>
                                    <small class="text-muted d-block">
                                        {{ getDataByLang($review, 'job_title') }}
                                    </small>
                                </div>
                            </div>
                            <!-- مثلاً أيقونة LinkedIn -->
                            <a href="#!"><img src="../assets/img/icons/linkedin-primary.svg" alt=""></a>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-stretch my-md-0 my-5">
                <img src="
                {{ asset($section->content['imageUrl'][0]) }}" class="img-fluid" alt="Proposal Writing Guide">
            </div>
            <div class="col-md-4 d-flex align-items-stretch">
                <div class="w-100 d-flex flex-column justify-content-between ">
                    <div>
                        <h2 class="fw-normal">
                            {{ getDataByLang($section->content['product'], 'title') }}
                        </h2>
                        <div class="d-flex align-items-center mb-3">
                            @if($section->content['product']['old_price'])
                            <span class="text-decoration-line-through fs-4 me-2">
                                ${{ $section->content['product']['old_price'] }}
                            </span>
                            @endif
                            <span class="price fw-semibold text-secondary ">
                                ${{ $section->content['product']['new_price'] }}
                            </span>
                        </div>
                    </div>

                    <ul class="list-unstyled mb-5 mt-3">
                        <li class="mb-3 d-flex align-items-center ">
                            <img src="{{asset('assets/img/icons/num-of-page.svg')}}" alt="">
                            <div class="ps-2">
                                <p >Number Of Pages:</p>
                                <span class="fw-bold text-primary">
                                    {{ $section->content['product']['no_of_items'] }}
                                </span>
                            </div>
                        </li>
                        <li class="d-flex align-items-center ">
                            <img src="{{asset('assets/img/icons/material.svg')}}" alt="">
                            <div class="ps-2">
                                <p>Supported With:</p>
                                <span class="fw-bold text-primary">
                               {{ $section->content['product']['supported_with'] }}

                                </span>
                            </div>
                        </li>
                    </ul>
                    <a data-bs-toggle="modal" data-bs-target="#proposalModal"
                       class="primary-outline-btn w-100 py-3 justify-content-between outline-btn">
                        Try it now
                        <img src="{{asset('assets/img/arrow.svg')}}" class="ms-5 ps-2" alt="" />
                    </a>
                </div>
            </div>
        </div>
    </div>
    @livewire('proposal-modal')
</section>
<!-- End Proposal Writing Guide -->

