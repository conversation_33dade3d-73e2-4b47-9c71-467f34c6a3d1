<div>
    @if (session()->has('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @endif

    <form wire:submit.prevent="register">
        <div class="auth-content d-flex flex-column justify-content-between pt-4 px-md-4 px-2">
            <div>
                <img src="{{ asset('assets/img/Logo.svg') }}" alt="">
                <p class="auth-title fw-bold my-4">Create your account now</p>

                <div class="row">
                    <div class="col-md-6 mb-md-4 mb-4">
                        <label for="">First name*</label>
                        <input type="text" wire:model="first_name" class="form-control" placeholder="Enter your first name">
                        @error('first_name') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-md-6 mb-md-4 mb-4">
                        <label for="">Family name*</label>
                        <input type="text" wire:model="last_name" class="form-control" placeholder="Enter your family name">
                        @error('last_name') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-md-6 mb-md-4 mb-4">
                        <label for="">Email*</label>
                        <input type="text" wire:model="email" class="form-control" placeholder="Enter your email">
                        @error('email') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-md-6 mb-md-4 mb-4">
                        <label for="">Password*</label>
                        <input type="password" wire:model="password" class="form-control" placeholder="Enter your password">
                        @error('password') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-md-6  mb-md-4 mb-4">
                        <label for="">Organization*</label>
                        <input type="text" wire:model="organization" class="form-control" placeholder="Enter your organization">
                        @error('organization') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-md-6  mb-md-4 mb-4">
                        <label for="">Mobile*</label>
                        <input type="number" wire:model="mobile" class="form-control" placeholder="+970">
                        @error('mobile') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-md-12 mb-4 row">
                        <h6>Sign up options*</h6>
                        <div class="col-md-6 mb-4">
                            <input type="radio" wire:model="type" value="free"> <label for="">Free</label> <br>
                            <span>You are one of Grants clients</span>
                        </div>
                        <div class="col-md-6 mb-4">
                            <input type="radio" wire:model="type" value="subscribe"> <label for="">Subscriptions</label> <br>
                            <span>{{ $subscriptionDescription }} for ({{ $subscriptionCurrency }} {{ $subscriptionAmount }})</span>
                        </div>
                        @error('type') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            <div class="row mt-md-0 mt-4 mb-1">
                <div class="col-12 text-center">
                    <button type="submit" class="w-100 py-3 mb-2" wire:loading.attr="disabled">
                        <span wire:loading.remove>Sign Up</span>
                        <span wire:loading>
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Processing...
                        </span>
                    </button>
                    <p class="mb-0">I have an account? <a href="{{ url('/sing-in') }}">Sign In</a></p>
                </div>
            </div>
        </div>
    </form>
</div>
