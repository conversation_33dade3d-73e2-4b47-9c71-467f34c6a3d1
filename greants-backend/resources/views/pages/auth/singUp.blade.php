<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>Grants | Sign up</title>
    <meta name="description" content="" />
    <meta name="keywords" content="" />

    <!-- Favicons -->
    <link href="{{asset('assets/img/logo.svg')}}" rel="icon" />
    <link href="{{asset('assets/img/logo.svg')}}" rel="apple-touch-icon" />

    <!-- Vendor CSS Files -->
    <link href="{{asset('assets/vendor/bootstrap/css/bootstrap.min.css')}}" rel="stylesheet" />
    <link href="{{asset('assets/vendor/bootstrap-icons/bootstrap-icons.css')}}" rel="stylesheet" />

    <!-- Main CSS File -->
    <link href="{{asset('assets/css/main.css')}}" rel="stylesheet" />

    <!-- Livewire Styles -->
    @livewireStyles

    <!-- Flasher Styles -->
    <link rel="stylesheet" href="{{ asset('vendor/flasher/flasher.min.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/flasher/toastr.min.css') }}">
</head>

<body class="auth-page d-flex  align-items-center">
<div class="container">
    <div class="row">
        <div class="col-md-8 order-md-0 order-1">
            @livewire('register-form')

        </div>
        <div class="col-md-4 px-4 my-3 order-md-1 order-0">
            <div class="auth-bg p-md-5 p-4">
                <svg width="32" height="17" viewBox="0 0 32 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M0.5 9.28C0.5 7.82933 0.947818 6.4 1.84345 4.992C2.78885 3.584 3.98303 2.41066 5.42599 1.472C6.91872 0.490667 8.48608 0 10.1281 0C10.6754 0 11.2476 0.192 11.8447 0.576C12.4916 0.96 12.815 1.62133 12.815 2.56C12.815 3.49866 12.4418 4.07467 11.6954 4.288C10.9988 4.50133 10.4017 4.71466 9.90417 4.928C9.25732 5.22667 8.73487 5.58933 8.33681 6.016C7.98851 6.4 7.81436 6.89067 7.81436 7.488C7.81436 7.91467 8.06314 8.32 8.56072 8.704C9.10805 9.088 9.80466 9.28 10.6505 9.28C10.9491 9.28 11.2974 9.25867 11.6954 9.216C12.0935 9.13066 12.4418 9.00267 12.7404 8.832C13.2877 9.00267 13.7355 9.25866 14.0838 9.6C14.4321 9.94133 14.6063 10.4747 14.6063 11.2C14.6063 12.5227 13.8848 13.6747 12.4418 14.656C11.0486 15.5947 9.4066 16.064 7.51581 16.064C5.37624 16.064 3.6596 15.4453 2.36591 14.208C1.12197 12.9707 0.5 11.328 0.5 9.28ZM16.9946 9.28C16.9946 7.82933 17.4424 6.4 18.3381 4.992C19.2835 3.584 20.4776 2.41066 21.9206 1.472C23.4133 0.490667 24.9807 0 26.6227 0C27.17 0 27.7422 0.192 28.3393 0.576C28.9862 0.96 29.3096 1.62133 29.3096 2.56C29.3096 3.49866 28.9364 4.07467 28.1901 4.288C27.4935 4.50133 26.8964 4.71466 26.3988 4.928C25.7519 5.22667 25.2295 5.58933 24.8314 6.016C24.4831 6.4 24.309 6.89067 24.309 7.488C24.309 7.91467 24.5578 8.32 25.0553 8.704C25.6027 9.088 26.2993 9.28 27.1452 9.28C27.4437 9.28 27.792 9.25867 28.1901 9.216C28.5881 9.13066 28.9364 9.00267 29.235 8.832C29.7823 9.00267 30.2301 9.25866 30.5784 9.6C30.9267 9.94133 31.1009 10.4747 31.1009 11.2C31.1009 12.5227 30.3794 13.6747 28.9364 14.656C27.5432 15.5947 25.9012 16.064 24.0104 16.064C21.8709 16.064 20.1542 15.4453 18.8605 14.208C17.6166 12.9707 16.9946 11.328 16.9946 9.28Z"
                        fill="white" />
                </svg>

                <p class="mb-0 text-white">
                    Together, let’s fuel the growth of resilient and impactful
                    organizations across Palestine
                </p>

                <div class="text-end">
                    <svg width="32" height="17" viewBox="0 0 32 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M0.5 9.28C0.5 7.82933 0.947818 6.4 1.84345 4.992C2.78885 3.584 3.98303 2.41066 5.42599 1.472C6.91872 0.490667 8.48608 0 10.1281 0C10.6754 0 11.2476 0.192 11.8447 0.576C12.4916 0.96 12.815 1.62133 12.815 2.56C12.815 3.49866 12.4418 4.07467 11.6954 4.288C10.9988 4.50133 10.4017 4.71466 9.90417 4.928C9.25732 5.22667 8.73487 5.58933 8.33681 6.016C7.98851 6.4 7.81436 6.89067 7.81436 7.488C7.81436 7.91467 8.06314 8.32 8.56072 8.704C9.10805 9.088 9.80466 9.28 10.6505 9.28C10.9491 9.28 11.2974 9.25867 11.6954 9.216C12.0935 9.13066 12.4418 9.00267 12.7404 8.832C13.2877 9.00267 13.7355 9.25866 14.0838 9.6C14.4321 9.94133 14.6063 10.4747 14.6063 11.2C14.6063 12.5227 13.8848 13.6747 12.4418 14.656C11.0486 15.5947 9.4066 16.064 7.51581 16.064C5.37624 16.064 3.6596 15.4453 2.36591 14.208C1.12197 12.9707 0.5 11.328 0.5 9.28ZM16.9946 9.28C16.9946 7.82933 17.4424 6.4 18.3381 4.992C19.2835 3.584 20.4776 2.41066 21.9206 1.472C23.4133 0.490667 24.9807 0 26.6227 0C27.17 0 27.7422 0.192 28.3393 0.576C28.9862 0.96 29.3096 1.62133 29.3096 2.56C29.3096 3.49866 28.9364 4.07467 28.1901 4.288C27.4935 4.50133 26.8964 4.71466 26.3988 4.928C25.7519 5.22667 25.2295 5.58933 24.8314 6.016C24.4831 6.4 24.309 6.89067 24.309 7.488C24.309 7.91467 24.5578 8.32 25.0553 8.704C25.6027 9.088 26.2993 9.28 27.1452 9.28C27.4437 9.28 27.792 9.25867 28.1901 9.216C28.5881 9.13066 28.9364 9.00267 29.235 8.832C29.7823 9.00267 30.2301 9.25866 30.5784 9.6C30.9267 9.94133 31.1009 10.4747 31.1009 11.2C31.1009 12.5227 30.3794 13.6747 28.9364 14.656C27.5432 15.5947 25.9012 16.064 24.0104 16.064C21.8709 16.064 20.1542 15.4453 18.8605 14.208C17.6166 12.9707 16.9946 11.328 16.9946 9.28Z"
                            fill="white" />
                    </svg>
                </div>

            </div>
        </div>
    </div>

</div>


<!-- Modal -->
<div class="modal fade modal-lg" id="signUpModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
     aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered ">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5">🌟 Discover the Power of Grants</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-md-4 p-3">
          <span class="gray-500 mb-4 d-inline-block">Learn how our platform empowers organizations to connect with
            global donors, secure funding, and drive meaningful change. Watch this short video to see What Grants
            is!</span>
                <iframe src="https://www.youtube.com/embed/d8x8hVpfLcc?si=WN6fav5J5UmDq3Q2"
                        title="YouTube video player" frameborder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>

<div class="modal fade modal-lg" id="SuccessModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
     aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered ">
        <div class="modal-content">
            <div class="modal-header border-0 ">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-md-4 p-3 mb-4">

                <div class="row justify-content-center text-center ">
                    <div class="col-md-4 col-5 mb-3">
                        <img src="{{asset('assets/img/Sucess.svg')}}" class="img-fluid" alt="">
                    </div>
                    <div class="col-md-10 text-center">
                        <p class="fs-2 text-secondary">🎉 Success! Your Action Was Completed!</p>
                        <p class="fs-6 gray-500 px-md-5 px-0 mt-3 mb-4">Thank you! Your request has been successfully processed.
                            We’re excited to have you on board and will be in touch shortly. If you have any questions, feel free to
                            reach out to us.</p>

                    </div>
                    <div class="col-md-6">
                        <a href="{{url('/')}}" class="secondary-outline-btn py-3 outline-btn justify-content-between w-100 ">
                            Continue
                            <img src="{{asset('assets/img/arrow-primary.svg')}}" alt="" />
                        </a>
                    </div>

                </div>

            </div>
        </div>
    </div>
</div>

<div class="modal fade modal-lg" id="PaymentModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
     aria-labelledby="paymentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5">Complete Your Subscription Payment</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-md-4 p-3">
                <div class="payment-container">
                    <div class="payment-details mb-4">
                        <h5>Subscription Details</h5>
                        <p>You're subscribing to Grants platform with the following details:</p>
                        <div class="card p-3 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Amount:</strong> <span id="payment-amount"></span> <span id="payment-currency"></span>
                                </div>
                                <div>
                                    <strong>Email:</strong> <span id="payment-email"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="payment-form">
                        <div class="d-grid gap-2">
                            <button type="button" id="pay-button" class="btn btn-primary py-3">
                                Proceed to Payment
                            </button>
                        </div>
                    </div>

                    <div class="payment-status mt-4" style="display: none;">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div>Processing your payment...</div>
                            </div>
                        </div>
                    </div>

                    <div class="payment-error mt-4" style="display: none;">
                        <div class="alert alert-danger">
                            <div id="error-message">An error occurred during payment processing.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Modal -->

<!-- Vendor JS Files -->
<script src="{{asset('assets/vendor/bootstrap/js/bootstrap.bundle.min.js')}}"></script>
<script src="{{asset('assets/vendor/imagesloaded/imagesloaded.pkgd.min.js')}}"></script>
<script src="{{asset('assets/vendor/isotope-layout/isotope.pkgd.min.js')}}"></script>
<script src="{{asset('assets/vendor/waypoints/noframework.waypoints.js')}}"></script>

<!-- Lahza Payment SDK -->
<script src="https://js.lahza.io/v1" async onload="console.log('Lahza SDK loaded')" onerror="console.error('Failed to load Lahza SDK')"></script>
<script>
    window.addEventListener('DOMContentLoaded', (event) => {
        const myModal = new bootstrap.Modal(document.getElementById('signUpModal'));
        myModal.show();

        // Initialize Lahza payment after SDK loads
        let lahza;
        const lahzaKey = '{{ config('services.lahza.key') }}';

        console.log('Lahza Key:', lahzaKey ? 'pk_***' + lahzaKey.slice(-4) : 'Not configured');

        // Function to initialize Lahza
        function initializeLahza() {
            // Check if Lahza is available
            if (typeof Lahza === 'undefined') {
                console.error('Lahza SDK not loaded');
                return false;
            }

            if (lahzaKey) {
                try {
                    lahza = Lahza(lahzaKey);
                    console.log('Lahza initialized successfully');
                    return true;
                } catch (error) {
                    console.error('Failed to initialize Lahza:', error);
                    return false;
                }
            } else {
                console.error('Lahza public key not configured');
                return false;
            }
        }

        // Try to initialize Lahza immediately
        initializeLahza();

        window.addEventListener('show-success-modal', event => {
            setTimeout(() => {
                var successModal = new bootstrap.Modal(document.getElementById('SuccessModal'));
                successModal.show();
            }, 300);
        });

        window.addEventListener('show-payment-modal', event => {
            setTimeout(() => {
                // Get payment details from the event
                const paymentDetails = event.detail || {};

                console.log('Payment details received:', paymentDetails);

                // Update the payment modal with the details
                const amountElement = document.getElementById('payment-amount');
                const currencyElement = document.getElementById('payment-currency');
                const emailElement = document.getElementById('payment-email');

                if (amountElement) amountElement.textContent = paymentDetails.amount || 'N/A';
                if (currencyElement) currencyElement.textContent = paymentDetails.currency || 'N/A';
                if (emailElement) emailElement.textContent = paymentDetails.email || 'N/A';

                // Show the payment modal
                var paymentModal = new bootstrap.Modal(document.getElementById('PaymentModal'));
                paymentModal.show();

                // Set up the pay button (remove any existing listeners first)
                const payButton = document.getElementById('pay-button');
                if (payButton) {
                    // Clone the button to remove all event listeners
                    const newPayButton = payButton.cloneNode(true);
                    payButton.parentNode.replaceChild(newPayButton, payButton);

                    // Add new event listener
                    newPayButton.addEventListener('click', function() {
                        initiatePayment(paymentDetails);
                    });
                }
            }, 300);
        });

        // Function to initiate payment with Lahza
        function initiatePayment(paymentDetails) {
            console.log('Initiating payment with details:', paymentDetails);
            console.log('Lahza instance:', lahza);

            // Try to initialize Lahza if not already done
            if (!lahza) {
                console.log('Attempting to initialize Lahza...');
                if (!initializeLahza()) {
                    showError('Payment gateway not initialized. Please try again later.');
                    return;
                }
            }

            if (!lahza) {
                console.error('Lahza not initialized');
                showError('Payment gateway not initialized. Please try again later.');
                return;
            }

            // Show loading state
            document.querySelector('.payment-status').style.display = 'block';
            document.querySelector('.payment-error').style.display = 'none';
            document.getElementById('pay-button').disabled = true;

            // Configure the payment
            lahza.configure({
                key: '{{ config('services.lahza.key') }}',
                amount: paymentDetails.amount * 100, // Lahza expects amount in cents
                currency: paymentDetails.currency,
                email: paymentDetails.email,
                ref: paymentDetails.reference,
                callback: function(response) {
                    // Payment successful
                    document.querySelector('.payment-status').style.display = 'none';

                    if (response.status === 'success') {
                        // Call Livewire method to complete the payment
                        Livewire.dispatch('completePayment', {
                            transactionReference: response.reference
                        });
                    } else {
                        showError('Payment was not successful. Please try again.');
                    }
                },
                onClose: function() {
                    // Payment window closed
                    document.querySelector('.payment-status').style.display = 'none';
                    document.getElementById('pay-button').disabled = false;
                },
                onError: function(error) {
                    // Payment error
                    document.querySelector('.payment-status').style.display = 'none';
                    showError('Payment failed: ' + (error.message || 'Unknown error occurred'));
                }
            });

            // Open the payment modal
            lahza.open();
        }

        // Function to show error message
        function showError(message) {
            const errorElement = document.getElementById('error-message');
            if (errorElement) {
                errorElement.textContent = message;
                document.querySelector('.payment-error').style.display = 'block';
                document.querySelector('.payment-status').style.display = 'none';
                document.getElementById('pay-button').disabled = false;
            }
        }

        // Listen for payment failed event
        window.addEventListener('payment-failed', () => {
            showError('Payment verification failed. Please try again or contact support.');
        });

        // Debug: Listen for Livewire events
        document.addEventListener('livewire:init', () => {
            console.log('Livewire initialized');
        });

        document.addEventListener('livewire:navigated', () => {
            console.log('Livewire navigated');
        });

        // Debug: Check if Livewire is available
        if (typeof Livewire !== 'undefined') {
            console.log('Livewire is available');
        } else {
            console.error('Livewire is not available');
        }
    });
</script>

<!-- Livewire Scripts -->
@livewireScripts
</body>

</html>
