!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).flasher=t()}(this,(function(){"use strict";function e(e,t,s,n){return new(s||(s=Promise))((function(o,i){function r(e){try{a(n.next(e))}catch(e){i(e)}}function l(e){try{a(n.throw(e))}catch(e){i(e)}}function a(e){var t;e.done?o(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(r,l)}a((n=n.apply(e,t||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;class t{success(e,t,s){this.flash("success",e,t,s)}error(e,t,s){this.flash("error",e,t,s)}info(e,t,s){this.flash("info",e,t,s)}warning(e,t,s){this.flash("warning",e,t,s)}flash(e,t,s,n){if("object"==typeof e?(e=(n=e).type,t=n.message,s=n.title):"object"==typeof t?(t=(n=t).message,s=n.title):"object"==typeof s&&(s=(n=s).title),void 0===t)throw new Error("message option is required");const o={type:e,message:t,title:s||e,options:n||{},metadata:{plugin:""}};this.renderOptions(n||{}),this.renderEnvelopes([o])}}class s extends t{constructor(e){super(),this.options={timeout:null,timeouts:{success:5e3,info:5e3,error:5e3,warning:5e3},fps:30,position:"top-right",direction:"top",rtl:!1,style:{},escapeHtml:!1},this.theme=e}renderEnvelopes(e){const t=()=>e.forEach((e=>{var t,s,n,o;const i=null!==(s=null!==(t=this.options.timeout)&&void 0!==t?t:this.options.timeouts[e.type])&&void 0!==s?s:5e3,r=Object.assign(Object.assign(Object.assign({},this.options),e.options),{timeout:null!==(n=e.options.timeout)&&void 0!==n?n:i,escapeHtml:null!==(o=e.options.escapeHtml)&&void 0!==o?o:this.options.escapeHtml});this.addToContainer(this.createContainer(r),e,r)}));"loading"===document.readyState?document.addEventListener("DOMContentLoaded",t):t()}renderOptions(e){this.options=Object.assign(Object.assign({},this.options),e)}createContainer(e){let t=document.querySelector(`.fl-wrapper[data-position="${e.position}"]`);return t||(t=document.createElement("div"),t.className="fl-wrapper",t.dataset.position=e.position,Object.entries(e.style).forEach((([e,s])=>t.style.setProperty(e,s))),document.body.appendChild(t)),t.dataset.turboTemporary="",t}addToContainer(e,t,s){var n;s.escapeHtml&&(t.title=this.escapeHtml(t.title),t.message=this.escapeHtml(t.message));const o=this.stringToHTML(this.theme.render(t));o.classList.add(...("fl-container"+(s.rtl?" fl-rtl":"")).split(" ")),"bottom"===s.direction?e.append(o):e.prepend(o),requestAnimationFrame((()=>o.classList.add("fl-show"))),null===(n=o.querySelector(".fl-close"))||void 0===n||n.addEventListener("click",(e=>{e.stopPropagation(),this.removeNotification(o)})),this.addProgressBar(o,s)}addProgressBar(e,{timeout:t,fps:s}){if(t<=0||s<=0)return;const n=e.querySelector(".fl-progress-bar");if(!n)return;const o=document.createElement("span");o.classList.add("fl-progress"),n.append(o);const i=1e3/s;let r=0;const l=()=>{r+=1;const s=100*(1-i*(r/t));o.style.width=`${s}%`,s<=0&&(clearInterval(a),this.removeNotification(e))};let a=window.setInterval(l,i);e.addEventListener("mouseout",(()=>a=window.setInterval(l,i))),e.addEventListener("mouseover",(()=>clearInterval(a)))}removeNotification(e){e.classList.remove("fl-show"),e.ontransitionend=()=>{var t,s;!(null===(t=e.parentElement)||void 0===t?void 0:t.hasChildNodes())&&(null===(s=e.parentElement)||void 0===s||s.remove()),e.remove()}}stringToHTML(e){const t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstElementChild}escapeHtml(e){return null==e?"":e.replace(/[&<>"'`=\/]/g,(e=>({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","`":"&#96;","=":"&#61;","/":"&#47;"}[e])))}}const n=new class extends t{constructor(){super(...arguments),this.defaultPlugin="flasher",this.plugins=new Map,this.themes=new Map}render(t){return e(this,void 0,void 0,(function*(){const e=this.resolveResponse(t);yield this.addAssets([{urls:e.styles,nonce:e.context.csp_style_nonce,type:"style"},{urls:e.scripts,nonce:e.context.csp_script_nonce,type:"script"}]),this.renderOptions(e.options),this.renderEnvelopes(e.envelopes)}))}renderEnvelopes(e){const t={};e.forEach((e=>{const s=this.resolvePluginAlias(e.metadata.plugin);t[s]=t[s]||[],t[s].push(e)})),Object.entries(t).forEach((([e,t])=>{this.use(e).renderEnvelopes(t)}))}renderOptions(e){Object.entries(e).forEach((([e,t])=>{this.use(e).renderOptions(t)}))}addPlugin(e,t){this.plugins.set(e,t)}addTheme(e,t){this.themes.set(e,t)}use(e){e=this.resolvePluginAlias(e),this.resolvePlugin(e);const t=this.plugins.get(e);if(!t)throw new Error(`Unable to resolve "${e}" plugin, did you forget to register it?`);return t}create(e){return this.use(e)}resolveResponse(e){const t=Object.assign({envelopes:[],options:{},scripts:[],styles:[],context:{}},e);return Object.entries(t.options).forEach((([e,s])=>{t.options[e]=this.resolveOptions(s)})),t.context.csp_style_nonce=t.context.csp_style_nonce||"",t.context.csp_script_nonce=t.context.csp_script_nonce||"",t.envelopes.forEach((s=>{s.metadata=s.metadata||{},s.metadata.plugin=this.resolvePluginAlias(s.metadata.plugin),this.addThemeStyles(t,s.metadata.plugin),s.options=this.resolveOptions(s.options),s.context=e.context})),t}resolveOptions(e){return Object.entries(e).forEach((([t,s])=>{e[t]=this.resolveFunction(s)})),e}resolveFunction(e){var t,s;if("string"!=typeof e)return e;const n=e.match(/^function\s*(\w*)\s*\(([^)]*)\)\s*\{([\s\S]*)\}$/)||e.match(/^\s*(\(([^)]*)\)|[^=]+)\s*=>\s*([\s\S]+)$/);if(!n)return e;const o=null!==(s=null===(t=n[2])||void 0===t?void 0:t.split(",").map((e=>e.trim())))&&void 0!==s?s:[];let i=n[3].trim();i.startsWith("{")||(i=`{ return ${i}; }`);try{return new Function(...o,i)}catch(t){return console.error("Error converting string to function:",t),e}}resolvePlugin(e){if(this.plugins.get(e)||!e.includes("theme."))return;const t=this.themes.get(e.replace("theme.",""));t&&this.addPlugin(e,new s(t))}resolvePluginAlias(e){return"flasher"===(e=e||this.defaultPlugin)?"theme.flasher":e}addAssets(t){return e(this,void 0,void 0,(function*(){for(const{urls:e,nonce:s,type:n}of t)for(const t of e)yield this.loadAsset(t,s,n)}))}loadAsset(t,s,n){return e(this,void 0,void 0,(function*(){if(document.querySelector(`${"style"===n?"link":"script"}[src="${t}"]`))return;const e=document.createElement("style"===n?"link":"script");return"style"===n?(e.rel="stylesheet",e.href=t):(e.type="text/javascript",e.src=t),s&&e.setAttribute("nonce",s),document.head.appendChild(e),new Promise(((s,n)=>{e.onload=()=>s(),e.onerror=()=>n(new Error(`Failed to load ${t}`))}))}))}addThemeStyles(e,t){var s;if("flasher"!==t&&!t.includes("theme."))return;t=t.replace("theme.","");const n=(null===(s=this.themes.get(t))||void 0===s?void 0:s.styles)||[];e.styles=Array.from(new Set([...e.styles,...n]))}};return n.addTheme("flasher",{render:e=>{const{type:t,title:s,message:n}=e,o="error"===t||"warning"===t;return`\n            <div class="fl-flasher fl-${t}" role="${o?"alert":"status"}" aria-live="${o?"assertive":"polite"}" aria-atomic="true">\n                <div class="fl-content">\n                    <div class="fl-icon"></div>\n                    <div>\n                        <strong class="fl-title">${s}</strong>\n                        <span class="fl-message">${n}</span>\n                    </div>\n                    <button class="fl-close" aria-label="Close ${t} message">&times;</button>\n                </div>\n                <span class="fl-progress-bar"></span>\n            </div>`}}),n}));
