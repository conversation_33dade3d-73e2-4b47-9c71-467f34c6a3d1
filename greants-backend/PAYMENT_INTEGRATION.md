# Payment Integration with Lahza

This document describes the payment integration implemented in the Grants application using the Lahza payment gateway.

## Overview

The payment integration is implemented for user registration subscriptions. When a user signs up and selects the "Subscriptions" option, they are prompted to complete payment through the Lahza payment gateway.

## Components

### 1. Configuration

The Lahza payment gateway is configured in `config/services.php`:

```php
'lahza' => [
    'key' => env('LAHZA_PUBLIC_KEY'),
    'secret' => env('LAHZA_SECRET_KEY'),
    'base_url' => env('LAHZA_API_URL', 'https://api.lahza.io'),
    'webhook_secret' => env('LAHZA_WEBHOOK_SECRET'),
    'currency' => env('LAHZA_CURRENCY', 'ILS'),
    'mode' => env('LAHZA_MODE', 'test'), // test or live
],
```

### 2. Environment Variables

Add these variables to your `.env` file:

```env
LAHZA_PUBLIC_KEY=your_public_key_here
LAHZA_SECRET_KEY=your_secret_key_here
LAHZA_API_URL=https://api.lahza.io
LAHZA_WEBHOOK_SECRET=your_webhook_secret_here
LAHZA_CURRENCY=ILS
LAHZA_MODE=test
```

### 3. Database Tables

The integration uses two main tables:

- `payments`: Stores payment records
- `payment_methods`: Stores saved payment methods for users

### 4. Models

- `Payment`: Handles payment records
- `PaymentMethod`: Handles saved payment methods
- `User`: Extended with subscription type handling

### 5. Services

- `LahzaPaymentService`: Handles all Lahza API interactions

### 6. Controllers

- `WebhookController`: Handles Lahza webhook notifications

### 7. Livewire Components

- `RegisterForm`: Handles user registration and payment flow

## Payment Flow

1. User fills out registration form
2. User selects "Subscriptions" option
3. User submits form
4. User account is created
5. Payment modal is displayed with subscription details
6. User clicks "Proceed to Payment"
7. Lahza payment window opens
8. User completes payment
9. Payment is verified with Lahza
10. User subscription status is updated
11. Success modal is displayed

## Webhook Handling

The webhook endpoint `/api/webhooks/lahza` handles payment notifications from Lahza:

- `charge.success`: Updates payment status to successful
- `charge.failed`: Updates payment status to failed

## Testing

To test the payment configuration, visit:
`/api/test/payment-config`

This will show if Lahza is properly configured.

## Security

- Webhook signatures are verified using HMAC
- Payment verification is done server-side
- Sensitive payment data is not stored locally

## Error Handling

The integration includes comprehensive error handling:

- JavaScript errors in payment modal
- API communication errors
- Webhook verification failures
- Payment verification failures

## Subscription Settings

Subscription amount and currency are managed through the settings system and can be updated from the admin dashboard.
