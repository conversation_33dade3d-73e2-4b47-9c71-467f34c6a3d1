<template>

  <el-form :class="type == 'view' ? 'form-read-only' : ''"
           v-loading="loadingData"
           :model="formData"
           :rules="rules"
           ref="formRef"
  >
    <div class="card mb-5 mb-xl-10">
      <div class="card-body pt-9 pb-0">
        <div class="row g-9 mb-7">
          <div class="col-md-6 fv-row" >
            <label class="required fs-6 fw-bold mb-2">Name</label>
            <el-form-item :prop="'name'" :rules="rules['field']">
              <el-input v-model="formData.name" :placeholder="`Name`"/>
            </el-form-item>
          </div>
          <div class="col-md-6 fv-row" >
            <label class="required fs-6 fw-bold mb-2">Email</label>
            <el-form-item :prop="'email'" :rules="rules['field']">
              <el-input v-model="formData.email" :placeholder="`Email`"/>
            </el-form-item>
          </div>
          <div class="col-md-6 fv-row" >
            <label class="required fs-6 fw-bold mb-2">Phone</label>
            <el-form-item :prop="'phone'" :rules="rules['field']">
              <el-input v-model="formData.phone" :placeholder="`phone`"/>
            </el-form-item>
          </div>

          <div class="col-md-12 fv-row" >
            <label class="required fs-6 fw-bold mb-2">Message</label>
            <el-form-item :prop="'message'" :rules="rules['field']">
              <el-input type="textarea" v-model="formData.message" :placeholder="`Message`"/>
            </el-form-item>
          </div>

          <div class="col-md-6 fv-row" >
            <label class="required fs-6 fw-bold mb-2">Is Replied ?</label>
            <el-form-item :prop="'is_replied'" :rules="rules['field']">
              <el-switch v-model="formData.is_replied" />
            </el-form-item>
          </div>

        </div>



      </div>
    </div>

    <div class="row g-9 mb-7">
      <div class="col-md-4 fv-row">

      </div>
      <div class="col-md-4 fv-row">
        <button v-if="type != 'view'"
                @click="submitForm()"
                :data-kt-indicator="loading ? 'on' : null"
                class="btn btn-lg btn-primary full-width"
                type="button"
                :disabled="loading">
              <span v-if="!loading" class="indicator-label">
                Submit
              </span>
          <span v-if="loading" class="indicator-progress">
                Please wait...
                <span
                    class="spinner-border spinner-border-sm align-middle ms-2"
                ></span>
              </span>
        </button>
      </div>
      <div class="col-md-4 fv-row">

      </div>
    </div>

  </el-form>

</template>

<script>
import {defineComponent, inject, onBeforeMount, onMounted, ref, toRefs} from "vue";
import {setCurrentPageBreadcrumbs} from "@/core/helpers/breadcrumb";
import {getIllustrationsPath} from "@/core/helpers/assets";
import {useStore} from "vuex";
import {useRoute} from 'vue-router'
import {Delete} from "@element-plus/icons-vue";
import ApiAxiosService from "@/core/services/ApiAxiosService";
import Swal from "sweetalert2/dist/sweetalert2.js";
import {handleError, handleSuccess} from "@/core/helpers/response";
import router from "@/router";
import {APIs} from "@/store/enums/ApiEnums";
import {ElMessage} from 'element-plus';

export default defineComponent({
  name: "city-comp",
  props: ['type'],
  setup(props) {
    const {type} = toRefs(props);
    const route = useRoute();
    const languages = ref(['en', 'ar']);
    const countries = ref([]);
    const features = ref([]);
    const store = useStore();
    const formRef = ref(null);
    const formData = ref({});
    const loading = ref(null);
    const loadingData = ref(false);

    const isIndeterminate = ref(true)

    formData.value = {
      name: null,
      email:null,
      phone : null,
      message : null,
      is_replied: false,

    };

    const rules = ref({
      field: [
        {
          required: true,
          message: "This field is required",
          trigger: "change",
        },
      ],
    });
    const initData = async () => {
      let response = null;
      loadingData.value = true;

      // Check if we have a notification ID to mark as read
      const notificationId = route.query.notification;
      if (notificationId) {
        markNotificationAsRead(notificationId);
      }

      switch (props.type) {
        case 'create' :
          try {

          }catch (e) {
            handleError(e)
          }

          break;
        case 'edit' :
          try {
            response = await ApiAxiosService.get(APIs.SUPORTREQUEST.edit(route.params.id));
            formData.value = response.data.data.support_request;
          }catch (e) {
            handleError(e)
          }

          break;
        case 'view' :
          try {
            response = await ApiAxiosService.get(APIs.SUPORTREQUEST.edit(route.params.id));
            formData.value = response.data.data.support_request;
          }catch (e) {
            handleError(e)
          }

          break;
      }
      loadingData.value = false;
    }

    // Mark notification as read
    const markNotificationAsRead = async (notificationId) => {
      try {
        await ApiAxiosService.post(APIs.AUTH.NOTIFICATIONS_MARK_READ, {
          notification_id: notificationId
        });

        ElMessage({
          message: 'Notification marked as read',
          type: 'success'
        });
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }

    const submitForm = () => {
      formRef.value.validate((valid) => {
        if (valid) {
          switch (props.type) {
            case 'create' :
              storeResource();
              break;
            case 'edit' :
              updateResource();
              break;
          }

        } else {
          Swal.fire({
            text: "Sorry, looks like there are some errors detected, please try again.",
            icon: "error",
            buttonsStyling: false,
            confirmButtonText: "Ok, got it!",
            customClass: {
              confirmButton: "btn btn-primary",
            },
          });
        }
      });
    }
    const storeResource = () => {
      loading.value = true;
      ApiAxiosService.post(APIs.SUPORTREQUEST.store, formData.value).then(function (res) {
        loading.value = null;
        handleSuccess(res);
        setTimeout(function () {
          router.push({name: 'opportunities-support'})
        }, 1000)
      }).catch(function (err) {
        loading.value = null;
        handleError(err);
      });
    }
    const updateResource = () => {
      loading.value = true;
      ApiAxiosService.put(APIs.SUPORTREQUEST.update(route.params.id), formData.value).then(function (res) {
        loading.value = null;
        handleSuccess(res);
        setTimeout(function () {
          router.push({name: 'opportunities-support'})
        }, 1000)
      }).catch(function (err) {
        loading.value = null;
        handleError(err);
      });
    }


    initData();


    return {
      getIllustrationsPath,
      formRef,
      formData,
      loading,
      loadingData,
      rules,
      languages,
      initData,
      countries,
      features,
      submitForm,
      storeResource,
      updateResource,
      type,
      isIndeterminate,
      Delete,
      markNotificationAsRead
    };
  },

});
</script>

