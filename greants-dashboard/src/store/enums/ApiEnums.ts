// @ts-ignore
// export const API_END_POINT = 'http://greants-backend.test/api';
// export const BASE_URL = "http://greants-backend.test/";
//



// export const API_END_POINT = 'https://grantsngo.ahdtech.com/api';
// export const BASE_URL = "https://grantsngo.ahdtech.com/";

export const API_END_POINT = 'http://127.0.0.1:8000/api';
export const BASE_URL = "http://127.0.0.1:8000/";

export const APIs = {

    AUTH: {
        LOGIN: `/admin/auth/login`,
        LOGOUT: `/admin/auth/logout`,
        SET_FCM_TOKEN: `/admin/auth/set-fcm-token`,
        NOTIFICATIONS: `/admin/notifications`,
        NOTIFICATIONS_MARK_READ: `/admin/notifications/mark-as-read`,
        NOTIFICATIONS_SEND: `/admin/notifications/send`,
        DASHBOARD_STATS: `/admin/dashboard/stats`,
        USER_CHART_DATA: `/admin/dashboard/user-chart`,
        OPPORTUNITY_CHART_DATA: `/admin/dashboard/opportunity-chart`,
        DASHBOARD_EXPORT_PDF: `/admin/dashboard/export/pdf`,
        DASHBOARD_EXPORT_EXCEL: `/admin/dashboard/export/excel`,
    },
    COUNTRY :{
        list : `/admin/countries`,
        create : `/admin/countries`,
        edit:(id)=>`/admin/countries/${id}`,
        store :  `/admin/countries`,
        update : (id)=>`/admin/countries/${id}`,
        delete : (id)=>`/admin/countries/${id}`,
        filter_options : `/admin/countries/filter-options`,
        actions : `/admin/countries/actions`,

    },

    FIELD_OPPORTUNITY :{
        list : `/admin/field-opportunities`,
        create : `/admin/field-opportunities/create`,
        edit:(id)=>`/admin/field-opportunities/${id}`,
        store :  `/admin/field-opportunities`,
        update : (id)=>`/admin/field-opportunities/${id}`,
        delete : (id)=>`/admin/field-opportunities/${id}`,
        filter_options : `/admin/field-opportunities/filter-options`,
        actions : `/admin/field-opportunities/actions`,
    },
    FIELD_DATABASE :{
        list : `/admin/field-databases`,
        create : `/admin/field-databases/create`,
        edit:(id)=>`/admin/field-databases/${id}`,
        store :  `/admin/field-databases`,
        update : (id)=>`/admin/field-databases/${id}`,
        delete : (id)=>`/admin/field-databases/${id}`,
        filter_options : `/admin/field-databases/filter-options`,
        actions : `/admin/field-databases/actions`,
    },
    FEES_COVERAGE :{
        list : `/admin/fees-coverages`,
        create : `/admin/fees-coverages/create`,
        edit:(id)=>`/admin/fees-coverages/${id}`,
        store :  `/admin/fees-coverages`,
        update : (id)=>`/admin/fees-coverages/${id}`,
        delete : (id)=>`/admin/fees-coverages/${id}`,
        filter_options : `/admin/fees-coverages/filter-options`,
        actions : `/admin/fees-coverages/actions`,

    },
    HOW_KNOW :{
        list : `/admin/how-knows`,
        create : `/admin/how-knows/create`,
        edit:(id)=>`/admin/how-knows/${id}`,
        store :  `/admin/how-knows`,
        update : (id)=>`/admin/how-knows/${id}`,
        delete : (id)=>`/admin/how-knows/${id}`,
        filter_options : `/admin/how-knows/filter-options`,
        actions : `/admin/how-knows/actions`,


    },
    SUBJECT :{
        list : `/admin/subjects`,
        create : `/admin/subjects/create`,
        edit:(id)=>`/admin/subjects/${id}`,
        store :  `/admin/subjects`,
        update : (id)=>`/admin/subjects/${id}`,
        delete : (id)=>`/admin/subjects/${id}`,
        filter_options : `/admin/subjects/filter-options`,
        actions : `/admin/subjects/actions`,


    },
    FUNDER :{
        list : `/admin/funders`,
        create : `/admin/funders/create`,
        edit:(id)=>`/admin/funders/${id}`,
        store :  `/admin/funders`,
        update : (id)=>`/admin/funders/${id}`,
        delete : (id)=>`/admin/funders/${id}`,
        filter_options : `/admin/funders/filter-options`,
        actions : `/admin/funders/actions`,
        export:'/admin/funders/export'

    },
    OPPORTUNITY :{
        list : `/admin/opportunities`,
        create : `/admin/opportunities/create`,
        edit:(id)=>`/admin/opportunities/${id}`,
        store :  `/admin/opportunities`,
        update : (id)=>`/admin/opportunities/${id}`,
        delete : (id)=>`/admin/opportunities/${id}`,
        filter_options : `/admin/opportunities/filter-options`,
        actions : `/admin/opportunities/actions`,
        export:'/admin/opportunities/export'

    },
    LANGUAGE :{
        list : `/admin/languages`,
        create : `/admin/languages/create`,
        edit:(id)=>`/admin/languages/${id}`,
        store :  `/admin/languages`,
        update : (id)=>`/admin/languages/${id}`,
        delete : (id)=>`/admin/languages/${id}`,
        filter_options : `/admin/languages/filter-options`,
        actions : `/admin/languages/actions`,


    },

    METHODOLOGY :{
        list : `/admin/methodologies`,
        create : `/admin/methodologies/create`,
        edit:(id)=>`/admin/methodologies/${id}`,
        store :  `/admin/methodologies`,
        update : (id)=>`/admin/methodologies/${id}`,
        delete : (id)=>`/admin/methodologies/${id}`,
        filter_options : `/admin/methodologies/filter-options`,
        actions : `/admin/methodologies/actions`,
    },
    TRAINING :{
        list : `/admin/trainings`,
        create : `/admin/trainings/create`,
        edit:(id)=>`/admin/trainings/${id}`,
        store :  `/admin/trainings`,
        update : (id)=>`/admin/trainings/${id}`,
        delete : (id)=>`/admin/trainings/${id}`,
        filter_options : `/admin/trainings/filter-options`,
        actions : `/admin/trainings/actions`,
    },
    COURSE_REGISTRATION: {
        list: `/admin/course-registrations`,
        show: (id) => `/admin/course-registrations/${id}`,
        stats: `/admin/course-registrations/stats`,
        filter_options: `/admin/course-registrations/filter-options`,
        actions: `/admin/course-registrations/actions`,
        update_status: (id) => `/admin/course-registrations/${id}/status`,
    },

  SECTION :{
    list : `/admin/sections`,
    },

    PAGE: {
        list: `/admin/pages`,
        create: `/admin/pages/create`,
        edit: (id) => `/admin/pages/${id}`,
        store: `/admin/pages`,
        update: (id) => `/admin/pages/${id}`,
        delete: (id) => `/admin/pages/${id}`,
        filter_options: `/admin/pages/filter-options`,
        actions: `/admin/pages/actions`,
        sections: (id)=>`admin/pages/${id}/sections`,
        create_section: (id)=>`admin/pages/${id}/sections/create`,
        store_section: (id)=>`admin/pages/${id}/section`,
        get_section: (id)=>`admin/pages/section/${id}`,
        update_section: (id)=>`admin/pages/section/${id}`,
        delete_section:(id) => `admin/pages/section/${id}`

    },
    CLIENT: {
        list: `/admin/clients`,
        create: `/admin/clients/create`,
        edit: (id) => `/admin/clients/${id}`,
        store: `/admin/clients`,
        update: (id) => `/admin/clients/${id}`,
        delete: (id) => `/admin/clients/${id}`,
        filter_options: `/admin/clients/filter-options`,
        actions: `/admin/clients/actions`,

    },

    USER :{
        list : `/admin/users`,
        create : `/admin/users/create`,
        edit:(id)=>`/admin/users/${id}`,
        store :  `/admin/users`,
        update : (id)=>`/admin/users/${id}`,
        delete : (id)=>`/admin/users/${id}`,
        filter_options : `/admin/users/filter-options`,
        actions : `/admin/users/actions`,


    },

    SUPORTREQUEST :{
        list : `/admin/support-requests`,
        edit:(id)=>`/admin/support-requests/${id}`,
        update : (id)=>`/admin/support-requests/${id}`,
        delete : (id)=>`/admin/support-requests/${id}`,
    },

    PARTNERSHIP :{
        list : `/admin/partnerships`,
        create : `/admin/partnerships/create`,
        edit:(id)=>`/admin/partnerships/${id}`,
        store :  `/admin/partnerships`,
        update : (id)=>`/admin/partnerships/${id}`,
        delete : (id)=>`/admin/partnerships/${id}`,
        filter_options : `/admin/partnerships/filter-options`,
        actions : `/admin/partnerships/actions`,
    },

    TAG :{
        list : `/admin/tags`,
        create : `/admin/tags/create`,
        edit:(id)=>`/admin/tags/${id}`,
        store :  `/admin/tags`,
        update : (id)=>`/admin/tags/${id}`,
        delete : (id)=>`/admin/tags/${id}`,
        filter_options : `/admin/tags/filter-options`,
        actions : `/admin/tags/actions`,

    },
     NEWS : {
        list: `/admin/news`,
        create: `/admin/news/create`,
        edit: (id) => `/admin/news/${id}`,
        store: `/admin/news`,
        update: (id) => `/admin/news/${id}`,
        delete: (id) => `/admin/news/${id}`,
        filter_options: `/admin/news/filter-options`,
        actions: `/admin/news/actions`,
     },

     TEAM:{
        list : `/admin/teams`,
        create : `/admin/teams/create`,
        edit:(id)=>`/admin/teams/${id}`,
        store :  `/admin/teams`,
        update : (id)=>`/admin/teams/${id}`,
        delete : (id)=>`/admin/teams/${id}`,
        filter_options : `/admin/teams/filter-options`,
        actions : `/admin/teams/actions`,
        upload_image: (id) => `/admin/teams/${id}/upload-image`, // Endpoint for image upload

     },

    PROPOSALBOOKINGS :{
        list : `/admin/proposal-bookings`,
        create : `/admin/proposal-bookings/create`,
        edit:(id)=>`/admin/proposal-bookings/${id}`,
        store :  `/admin/proposal-bookings`,
        update : (id)=>`/admin/proposal-bookings/${id}`,
        delete : (id)=>`/admin/proposal-bookings/${id}`,
        filter_options : `/admin/proposal-bookings/filter-options`,
        actions : `/admin/proposal-bookings/actions`,

    }





    ,

    // Role and Permission APIs
    ROLE: {
        list: `/admin/roles`,
        create: `/admin/roles/create`,
        edit: (id) => `/admin/roles/${id}`,
        store: `/admin/roles`,
        update: (id) => `/admin/roles/${id}`,
        delete: (id) => `/admin/roles/${id}`,
        filter_options: `/admin/roles/filter-options`,
    },

    PERMISSION: {
        list: `/admin/permissions`,
        create: `/admin/permissions/create`,
        edit: (id) => `/admin/permissions/${id}`,
        store: `/admin/permissions`,
        update: (id) => `/admin/permissions/${id}`,
        delete: (id) => `/admin/permissions/${id}`,
        filter_options: `/admin/permissions/filter-options`,
    }
}

